configs.com.patsnap.app.region=CN

configs.com.patsnap.resttemplate.logging.max-payload-length=20480000

## Redis config
#configs.com.patsnap.redis.redisson.cluster-servers-config.node-addresses[0]=redis://rc-core-ci.patsnap.info:6379

#Postgre SQL config
spring.datasource.dynamic.datasource.telecom.url=***********************************************************************

#S3 bucket
configs.com.patsnap.analytics.storage.bucket-domain=qa-patsnap-import-workspace.cdn.zhihuiya.com

# Rest Template - Identity
configs.com.patsnap.resttemplate.identity.base-uri=http://qa-s-platform-identity.patsnap.info/identity
com.patsnap.compute.similar.tdoc-url=http://qa-s-patsnaprd-tdoc-to-tdoc.patsnap.info/compute/tdoc_to_tdoc/
com.patsnap.analytics.service.patentapi-url=http://qa-s-platform-patent-api.patsnap.info/openapi
configs.com.patsnap.signature.url=http://qa-s-platform-signature.patsnap.info/signature/3.1
configs.com.patsnap.compute.patsnap-check-input.url=http://qa-s-patsnaprd-patsnap-check-input.patsnap.info/compute/patsnap_check_input/
com.patsnap.analytics.service.analytics-url=http://qa-s-analytics-basic.patsnap.info/analytics
com.patsnap.analytics.service.gpt.base-url=http://qa-s-patsnaprd-gateway.patsnap.info/v1
com.patsnap.analytics.service.ai-lab-disclosure-url=http://qa-s-patsnaprd-ai-lab-disclosure.patsnap.info/compute/ai_lab_disclosure/
com.patsnap.analytics.service.email.host=https://qa-analytics.zhihuiya.com
com.patsnap.ai.task.startup.enabled=false

configs.com.patsnap.compute.lang-detect.url=https://qa-s-patsnaprd-lang-detect.patsnap.info/compute/lang_detect/
configs.com.patsnap.compute.ipc.url=http://qa-s-patsnaprd-ipc-codefull-cn.patsnap.info/compute/ipc_codefull_cn/
configs.com.patsnap.compute.cpc.url=http://qa-s-patsnaprd-cpc-codefull-en.patsnap.info/compute/cpc_codefull_en/

com.patsnap.analytics.service.draw-narrator-url=http://qa-s-patsnaprd-drawing-narrator.patsnap.info/compute

configs.com.patsnap.patent_api.url=http://qa-s-platform-patent-api.patsnap.info/openapi/3.2.0
configs.com.patsnap.patent_aggregate_api.url=http://qa-s-analytics-patent-data.patsnap.info/patent-data

#claim parser
configs.com.patsnap.claimparser.service.url=http://s-gateway-qa-tencent.patsnap.io/claimparser-service/claim_parser

#specification drafting
configs.com.patsnap.compute.specification.url=http://qa-s-patsnaprd-patent-drafting.patsnap.info/compute/patent_drafting/
com.patsnap.analytics.service.eureka-url=http://qa-s-core-eureka.patsnap.info/eureka
# ai novelty search
com.patsnap.analytics.service.ai-search-url=http://qa-s-search-ai-search.patsnap.info
com.patsnap.analytics.service.ai-search-url2=http://qa-s-search-ai-search-v2.patsnap.info
com.patsnap.analytics.service.ai-novelty-search-compute-url=http://qa-s-patsnaprd-novelty-ai-search.patsnap.info/compute

com.patsnap.analytics.service.ai-novelty-search-compute-url-v2=http://qa-s-patsnaprd-novelty-ai-search-v2.patsnap.info/compute
configs.com.patsnap.open-api.url=http://qa-s-data-business-openapi.patsnap.info/business-openapi
com.patsnap.analytics.service.ai-agent-url=http://qa-s-core-agents-service.patsnap.info
com.patsnap.analytics.service.search-api-url=http://qa-s-search-search-api.patsnap.info/searchapi/3.0.0


# 创建应用时填写的AppId
app.id=s-analytics-telecom
# 自定义缓存路径，配置后若config service无法连接，则会使用本地缓存
#apollo.cache-dir=./data/
# 指定集群，当前只有一个default集群可以不配置
apollo.cluster=default
# Apollo配置中心地址
apollo.meta=http://10.194.32.21:8081
# 运行时自动更新使用Placeholder来注入的配置
apollo.auto-update-injected-spring-properties=true
# 启用Apollo的自动配置
apollo.bootstrap.enabled=true
# 指定namespace
apollo.bootstrap.namespaces=application
# Apollo的加载顺序放到日志系统加载之前
apollo.bootstrap.eager-load.enabled=false

# MyBatis SQL 日志配置
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
# 打印执行的 SQL 语句
logging.level.com.patsnap.drafting.repository=DEBUG

xxl.job.accessToken=xxl_job_cn_qa_zhihuiya
xxl.job.executor.appname=xxl-job-executor-telecom-local

configs.com.patsnap.security.key-management.kms.provider=

configs.com.patsnap.security.key-management.kms.tencent.region=
configs.com.patsnap.security.key-management.kms.tencent.secret-id=
configs.com.patsnap.security.key-management.kms.tencent.secret-key=
configs.com.patsnap.security.key-management.kms.tencent.key-id=
configs.com.patsnap.security.key-management.kms.tencent.connect-timeout=5000
configs.com.patsnap.security.key-management.kms.tencent.read-timeout=10000

configs.com.patsnap.security.key-management.kms.aws.region=
configs.com.patsnap.security.key-management.kms.aws.secret-id=
configs.com.patsnap.security.key-management.kms.aws.secret-key=
configs.com.patsnap.security.key-management.kms.aws.key-id=
configs.com.patsnap.security.key-management.kms.aws.connect-timeout=5000
configs.com.patsnap.security.key-management.kms.aws.read-timeout=10000

# \u6587\u4EF6\u4E0A\u4F20\u914D\u7F6E - \u672C\u5730\u5F00\u53D1\u73AF\u5883
spring.servlet.multipart.max-file-size=5MB

# fto history data date
configs.com.patsnap.drafting.fto.history-date=1752163200000
