configs.com.patsnap.app.simple.code=33
#
# ----------------------------------------
#  SPRING BOOT SERVER CONFIGURATION
# ----------------------------------------
#
## context path of the application
server.servlet.context-path=/telecom
# 2MB
server.max-http-header-size=2097152

#
# ----------------------------------------
# ****     SPRING BOOT CONFIG    *********t
# ----------------------------------------
# ----------------------------------------

#   SPRING MVC
# ----------------------------------------
#
spring.application.name=s-analytics-telecom
spring.web.resources.add-mappings=false

#
# ----------------------------------------
#   SPRING BOOT ACTUATOR / MANAGEMENT
# ----------------------------------------
management.endpoints.web.base-path=/manage
management.server.port=8080
management.endpoint.health.show-details=always
management.endpoints.web.exposure.include=health,info
management.endpoint.health.enabled=true
management.endpoint.env.enabled=true
management.endpoints.web.cors.allow-credentials=true
management.endpoints.web.cors.allowed-origin-patterns=*
# ----------------------------------------
#   SPRING MESSAGES i18n
# ----------------------------------------
#
spring.messages.basename=i18n/messages
spring.messages.encoding=UTF-8

# info
management.endpoint.info.enabled=true
management.info.env.enabled=true
management.info.build.enabled=true
management.info.java.enabled=true

# JMX
spring.jmx.default-domain=${spring.application.name}


# ----------------------------------------------------
# SWAGGER UI
configs.com.patsnap.swagger-ui.enabled=true
# App Config
configs.com.patsnap.request.logging.enabled=true

# -------------------------------------------
# web request logging
# -------------------------------------------
configs.com.patsnap.web.request.logging.enabled=true
configs.com.patsnap.web.request.logging.include-query-string=true
configs.com.patsnap.web.request.logging.include-client-info=true
configs.com.patsnap.web.request.logging.include-headers=false
configs.com.patsnap.web.request.logging.include-payload=false
configs.com.patsnap.web.filter.cors.enabled=true
configs.com.patsnap.web.filter.cors.url-path=/v2/api-docs
configs.com.patsnap.tools.xray.http-client.trace-downstream.enabled=false
configs.com.patsnap.core.error.response.wrapper.enable=true

# headers validation
configs.com.patsnap.web.request.headers.x-patsnap-from.required=true
configs.com.patsnap.web.request.headers.x-patsnap-from.origin-prefix=s-analytics-telecom
configs.com.patsnap.web.request.headers.x-patsnap-from.rs.white-list=manage,error,swagger,api-docs,ping,health
configs.com.patsnap.web.request.headers.x-user-id.required=false
configs.com.patsnap.web.request.headers.x-user-id.rs.white-list=manage,error,swagger,api-docs,ping,health
configs.com.patsnap.web.request.headers.x-patsnap-from.verify.format.enabled=false
configs.com.patsnap.web.request.logging.white-list=/health/check
# -------------------------------------------

# Rest Template - logging
# -------------------------------------------
configs.com.patsnap.resttemplate.logging.enabled=true
configs.com.patsnap.resttemplate.logging.include-headers=true
configs.com.patsnap.resttemplate.logging.include-payload=true
configs.com.patsnap.resttemplate.logging.include-response-payload=false
# unit byte, i.e. 5kb
configs.com.patsnap.resttemplate.logging.max-payload-length=1024

# resttemplate values below are default
configs.com.patsnap.resttemplate.max-conn-total=200
configs.com.patsnap.resttemplate.max-conn-per-route=100
configs.com.patsnap.resttemplate.connect-timeout=1000
configs.com.patsnap.resttemplate.read-timeout=30000
configs.com.patsnap.resttemplate.conn-request-timeout=2000
configs.com.patsnap.resttemplate.retry-count=1
configs.com.patsnap.resttemplate.request-sent-retry-enabled=true
configs.com.patsnap.resttemplate.encoding-mode=URI_COMPONENT

configs.com.patsnap.web.request.logging.max-payload-length=1024

spring.mvc.pathmatch.matching-strategy=ant-path-matcher

# 文件上传配置
spring.servlet.multipart.max-file-size=5MB

############################ business -------------------------------------------------------------
# Rest Template - Identity
configs.com.patsnap.resttemplate.identity.enabled=true
configs.com.patsnap.resttemplate.identity.base-uri=http://s-platform-identity.platform/identity
configs.com.patsnap.resttemplate.identity.client-id=
configs.com.patsnap.resttemplate.identity.client-secret=

com.patsnap.compute.similar.tdoc-url=


#开启searchApi接口熔断
#configs.com.patsnap.sentinel.searchapi.enable=true
##慢请求时间定义，超过这个时间定位为慢请求单位ms
#configs.com.patsnap.sentinel.searchapi.max_request_time=5000
##阈值 在窗口期之间的慢请求比例超过配置值，会发生熔断
#configs.com.patsnap.sentinel.searchapi.slow_ratio_threshold=0.25
##窗口期之间少于次请求量，不触发熔断
#configs.com.patsnap.sentinel.searchapi.min_request_amount=1500

## Redis config
configs.com.patsnap.redis.redisson.cluster-servers-config.node-addresses[0]=${configs.com.patsnap.redis.redisson.analytics-cluster.cluster-servers-config.node-addresses[0]}

#Postgre SQL config
spring.datasource.dynamic.primary=telecom
spring.datasource.dynamic.strict=true
spring.datasource.dynamic.datasource.telecom.url=
spring.datasource.dynamic.datasource.telecom.driver-class-name=org.postgresql.Driver
spring.datasource.dynamic.datasource.telecom.username=
spring.datasource.dynamic.datasource.telecom.password=

#COS bucket
configs.com.patsnap.cos.bucket-name=data-patent-comm-std-prod-bj-1251949819
configs.com.patsnap.cos.region=ap-beijing
configs.com.patsnap.cos.bucket-type=patent-comm-std

#S3 config
configs.com.patsnap.analytics.storage.use-s3=false
configs.com.patsnap.analytics.region=CN

# message template config
configs.com.patsnap.resttemplate.message.enabled=true

# AI Task SQS
configs.com.patsnap.data.sqs.ai-task.queue-name=core_telecom_ai_task_ci
configs.com.patsnap.data.sqs.ai-task.credentials.access-key=
configs.com.patsnap.data.sqs.ai-task.credentials.secret-key=
configs.com.patsnap.data.sqs.ai-task.credentials.region=cn-northwest-1
configs.com.patsnap.data.sqs.ai-task.credentials.uri=https://sqs.cn-northwest-1.amazonaws.com.cn

# service-url
com.patsnap.analytics.service.gpt.base-url=http://s-patsnaprd-gateway.patsnaprd/v1
com.patsnap.analytics.service.ai-lab-disclosure-url=http://s-patsnaprd-ai-lab-disclosure.patsnaprd/compute/ai_lab_disclosure/
configs.com.patsnap.signature.url=http://s-platform-signature.platform/signature/3.1
com.patsnap.analytics.service.analytics.url=http://s-analytics-basic.analytics/analytics
configs.com.patsnap.compute.patsnap-check-input.url=http://s-patsnaprd-patsnap-check-input.patsnaprd/compute/patsnap_check_input/
com.patsnap.analytics.service.analytics-url=${com.patsnap.analytics.service.basic-url}
com.patsnap.analytics.service.eureka-url=http://s-core-eureka.core/eureka

configs.com.patsnap.compute.lang-detect.url=http://s-patsnaprd-lang-detect.patsnaprd/compute/lang_detect/
configs.com.patsnap.compute.cpc.url=http://s-patsnaprd-cpc-codefull-en.patsnaprd/compute/cpc_codefull_en/
#specification drafting
configs.com.patsnap.compute.specification.url=http://s-patsnaprd-patent-drafting.patsnaprd/compute/patent_drafting/
configs.com.patsnap.compute.ipc.url=http://s-patsnaprd-ipc-codefull-cn.patsnaprd/compute/ipc_codefull_cn/
configs.com.patsnap.claimparser.service.url=http://s-patsnaprd-claimparser.patsnaprd/claimparser-service/claim_parser

configs.com.patsnap.patent_api.url=http://s-platform-patent-api.platform/openapi/3.2.0
configs.com.patsnap.patent_aggregate_api.url=http://s-analytics-patent-data.analytics/patent-data

com.patsnap.analytics.service.ai-search-url=http://s-search-ai-search.search
com.patsnap.analytics.service.ai-search-url2=http://s-search-ai-search-v2.search
com.patsnap.analytics.service.ai-agent-url=http://s-core-agents-service.core

com.patsnap.analytics.service.ai-novelty-search-compute-url=http://s-patsnaprd-novelty-ai-search.patsnaprd/compute
com.patsnap.analytics.service.ai-novelty-search-compute-url-v2=http://s-patsnaprd-novelty-ai-search-v2.patsnaprd/compute
configs.com.patsnap.open-api.url=http://s-data-business-openapi.data/business-openapi
com.patsnap.analytics.service.search-api-url=http://s-search-search-api.search/searchapi/3.0.0
com.patsnap.analytics.service.draw-narrator-url=http://s-patsnaprd-drawing-narrator.patsnaprd/compute

configs.com.patsnap.account.eu.customize.companies=
configs.com.patsnap.account.eu.customize.analytics.host=

# xxl-job配置
config.com.patsnap.xxl.job.enabled=true
### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.admin.addresses=http://qa-w-platform-xxl-job-service.patsnap.info/xxl-job-admin
### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken=
### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname=xxl-job-executor-telecom
### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
xxl.job.executor.address=
### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
xxl.job.executor.ip=
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port=9998
### 执行器日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler

xxl.job.executor.logretentiondays=30

configs.com.patsnap.biz-security.encrypt.enabled=false

# 内存监控配置
memory.monitor.enabled=true
