<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>telecom-parent</artifactId>
        <groupId>com.patsnap.analytics</groupId>
        <version>1.1-SNAPSHOT</version>
        <relativePath>../telecom-parent</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>telecom-core</artifactId>
    <packaging>jar</packaging>
    <name>PatSnap :: Analytics :: Telecom :: Core</name>

    <dependencies>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>telecom-api</artifactId>
            <version>${analytics.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.core</groupId>
            <artifactId>core-spring-boot-starter-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.core</groupId>
            <artifactId>core-spring-boot-starter-commonclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.core</groupId>
            <artifactId>core-spring-boot-starter-highlight</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.patsnap.core</groupId>
            <artifactId>core-spring-boot-starter-storage</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>common-restclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.core</groupId>
            <artifactId>core-spring-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>spring-boot-starter-resttemplate</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>spring-boot-starter-sqs</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.core.common</groupId>
            <artifactId>common-bizsecurity</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>


        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>common-redisson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>spring-boot-starter-mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>common-mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>common-orika</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.patsnap.common</groupId>
                    <artifactId>common-dynamodb</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>


        <dependency>
            <groupId>com.patsnap.core</groupId>
            <artifactId>core-spring-boot-starter-patentsource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.core</groupId>
            <artifactId>core-spring-boot-starter-acl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.core</groupId>
            <artifactId>core-spring-boot-starter-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.patsnap.common</groupId>
            <artifactId>common-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.7.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.31</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>ooxml-schemas</artifactId>
            <version>1.4</version> <!-- 或者更高版本 -->
        </dependency>


        <!-- Mockito 依赖 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>5.3.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>5.3.1</version>
            <scope>test</scope>
        </dependency>

        <!-- ByteBuddy 依赖 -->
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.14.5</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.14.5</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <!-- Retrofit 相关依赖 -->
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-jackson</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
            <version>4.9.3</version>
        </dependency>

        <!-- Spring Retry 依赖 -->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- source attach plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- classfier tests -->
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                        <configuration>
                            <includes>
                                <include>org/springside/modules/test/**/*.class</include>
                                <include>jetty/webdefault-windows.xml</include>
                            </includes>
                            <excludes>
                                <exclude>org/springside/modules/test/**/*Test.class</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>2.16</version>
                <executions>
                    <execution>
                        <id>integration-test</id>
                        <goals>
                            <goal>integration-test</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>verify</id>
                        <goals>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
