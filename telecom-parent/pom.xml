<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.patsnap.core</groupId>
        <artifactId>core-spring-boot-dependencies</artifactId>
        <version>Develop.2024.6.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>telecom-parent</artifactId>
    <groupId>com.patsnap.analytics</groupId>
    <version>1.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>PatSnap :: Analytics :: Telecom :: Parent</name>

    <properties>
        <analytics.version>1.1-SNAPSHOT</analytics.version>
        <java.version>17</java.version>
        <okhttp.version>4.12.0</okhttp.version>
        <xxl-job.version>2.3.0</xxl-job.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--Analytics Modules-->
            <dependency>
                <groupId>com.patsnap.analytics</groupId>
                <artifactId>analytics-core</artifactId>
                <version>${analytics.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.patsnap.analytics</groupId>
                <artifactId>analytics-api</artifactId>
                <version>${analytics.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!-- Common MODULES begin -->
            <dependency>
                <groupId>com.patsnap.common</groupId>
                <artifactId>common-parent</artifactId>
                <version>${common.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-sse</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.docx</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.docx</include>
                </includes>
            </resource>
        </resources>

        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.0</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.3.0</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>cq</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-pmd-plugin</artifactId>
                        <version>3.21.0</version>
                        <configuration>
                            <targetJdk>${java.version}</targetJdk>
                            <!--                            <sourceEncoding>utf-8</sourceEncoding>-->
                            <minimumTokens>100</minimumTokens>
                            <printFailingErrors>true</printFailingErrors>
                            <excludeFromFailureFile>${basedir}/../exclude-pmd.properties</excludeFromFailureFile>
                            <analysisCache>true</analysisCache>
                            <analysisCacheLocation>${project.build.directory}/pmd/pmd.cache</analysisCacheLocation>
                            <rulesets>
                                <ruleset>${basedir}/../pmd-rulesets.xml</ruleset>
                            </rulesets>
                            <excludes>
                                <exclude>**/*Test.java</exclude>
                                <exclude>**/*Create.java</exclude>
                                <exclude>**/*Info.java</exclude>
                                <exclude>**/*Update.java</exclude>
                                <exclude>**/*LocalFarmer.java</exclude>
                                <exclude>**/HighlightUtilV2.java</exclude>
                                <exclude>**/PatentChemical*.java</exclude>
                                <exclude>**/PatentPolymer*.java</exclude>
                                <exclude>**/LicenseHandler.java</exclude>
                            </excludes>
                            <excludeRoots>
                                <excludeRoot>target/generated-sources/stubs</excludeRoot>
                            </excludeRoots>
                        </configuration>
                        <executions>
                            <execution>
                                <id>pmd</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>check</goal>
                                    <goal>cpd-check</goal>
                                </goals>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>com.patsnap</groupId>
                                <artifactId>p3c-pmd</artifactId>
                                <version>1.3.4</version>
                            </dependency>
                        </dependencies>
                    </plugin>

                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
